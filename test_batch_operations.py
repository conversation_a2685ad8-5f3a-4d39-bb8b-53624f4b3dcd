#!/usr/bin/env python3
"""
测试批量复制和导出兑换码功能
"""

import requests
import json
import time

# 测试配置
BASE_URL = "http://localhost:7799"
ADMIN_USERNAME = "admin"  # 需要替换为实际的管理员用户名
ADMIN_PASSWORD = "admin"  # 需要替换为实际的管理员密码

def login_admin():
    """管理员登录"""
    login_data = {
        "username": ADMIN_USERNAME,
        "password": ADMIN_PASSWORD
    }
    
    session = requests.Session()
    response = session.post(f"{BASE_URL}/login", json=login_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            print("✓ 管理员登录成功")
            return session
        else:
            print(f"✗ 登录失败: {result.get('message')}")
            return None
    else:
        print(f"✗ 登录请求失败: {response.status_code}")
        return None

def create_test_codes(session, count=5):
    """创建测试兑换码"""
    create_data = {
        "count": count,
        "points": 10,
        "expire_days": 30,
        "description": "测试批量操作",
        "type": "one_time"
    }
    
    response = session.post(f"{BASE_URL}/admin/create_redemption_codes", json=create_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            codes = result.get('codes', [])
            print(f"✓ 成功创建 {len(codes)} 个测试兑换码")
            return codes
        else:
            print(f"✗ 创建兑换码失败: {result.get('message')}")
            return []
    else:
        print(f"✗ 创建兑换码请求失败: {response.status_code}")
        return []

def test_export_codes(session, codes):
    """测试导出兑换码功能"""
    export_data = {
        "codes": codes[:3]  # 导出前3个兑换码
    }
    
    response = session.post(f"{BASE_URL}/admin/export_redemption_codes", json=export_data)
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            export_data = result.get('export_data', [])
            print(f"✓ 成功导出 {len(export_data)} 个兑换码信息")
            
            # 显示导出的数据
            print("导出的兑换码信息:")
            for item in export_data:
                print(f"  - {item['code']}: {item['points']}积分, {item['type']}, {'有效' if item['is_active'] else '无效'}")
            
            return True
        else:
            print(f"✗ 导出失败: {result.get('message')}")
            return False
    else:
        print(f"✗ 导出请求失败: {response.status_code}")
        return False

def get_all_codes(session):
    """获取所有兑换码"""
    response = session.get(f"{BASE_URL}/admin/redemption_codes")
    
    if response.status_code == 200:
        result = response.json()
        if result.get('success'):
            codes = result.get('codes', [])
            print(f"✓ 获取到 {len(codes)} 个兑换码")
            return codes
        else:
            print(f"✗ 获取兑换码失败: {result.get('message')}")
            return []
    else:
        print(f"✗ 获取兑换码请求失败: {response.status_code}")
        return []

def main():
    """主测试函数"""
    print("开始测试批量复制和导出兑换码功能...")
    print("=" * 50)
    
    # 1. 管理员登录
    session = login_admin()
    if not session:
        print("无法登录，测试终止")
        return
    
    # 2. 创建测试兑换码
    print("\n创建测试兑换码...")
    test_codes = create_test_codes(session, 5)
    if not test_codes:
        print("无法创建测试兑换码，测试终止")
        return
    
    # 3. 获取所有兑换码
    print("\n获取所有兑换码...")
    all_codes = get_all_codes(session)
    
    # 4. 测试导出功能
    print("\n测试导出功能...")
    if test_codes:
        success = test_export_codes(session, test_codes)
        if success:
            print("✓ 导出功能测试通过")
        else:
            print("✗ 导出功能测试失败")
    
    print("\n" + "=" * 50)
    print("测试完成！")
    
    # 显示测试结果摘要
    print(f"\n测试摘要:")
    print(f"- 创建的测试兑换码: {len(test_codes)}")
    print(f"- 系统中总兑换码数: {len(all_codes)}")
    print(f"- 导出功能: {'正常' if test_codes else '异常'}")

if __name__ == "__main__":
    main()
