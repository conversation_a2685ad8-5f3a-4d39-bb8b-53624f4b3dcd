# 兑换码批量操作功能说明

## 功能概述

为管理员面板的兑换码管理模块新增了批量复制和导出功能，方便管理员高效管理大量兑换码。

## 新增功能

### 1. 批量复制功能
- **功能描述**: 可以选择多个兑换码并一键复制到剪贴板
- **使用方法**: 
  1. 在兑换码列表中勾选需要复制的兑换码
  2. 点击"批量复制"按钮
  3. 兑换码将以换行符分隔的格式复制到剪贴板
- **支持格式**: 每行一个兑换码，便于粘贴到其他应用程序

### 2. 批量导出功能
- **功能描述**: 将选中的兑换码导出为CSV文件，包含详细信息
- **使用方法**:
  1. 在兑换码列表中勾选需要导出的兑换码
  2. 点击"批量导出"按钮
  3. 系统将自动下载包含兑换码信息的CSV文件
- **导出内容**:
  - 兑换码
  - 积分数量
  - 类型（一次性/活动码）
  - 状态（有效/已禁用/已过期）
  - 创建时间
  - 过期时间
  - 描述
  - 使用次数

### 3. 选择操作增强
- **全选功能**: 一键选择所有兑换码
- **清空选择**: 一键清空所有选择
- **主复选框**: 表头的复选框可以控制所有兑换码的选择状态

## 界面改进

### 操作按钮布局
```
[全选] [清空]                    [批量复制] [批量导出] [批量启用] [批量禁用] [批量删除]
```

### 兑换码列表
- 每行前面增加复选框，支持单独选择
- 表头增加主复选框，支持全选/全不选
- 保持原有的单个操作功能（启用/禁用/复制/删除）

## 技术实现

### 前端功能
1. **批量复制**:
   - 使用现代剪贴板API (`navigator.clipboard`)
   - 提供备用方案 (`document.execCommand`)
   - 支持多种浏览器环境

2. **批量导出**:
   - 调用后端API获取详细信息
   - 客户端生成CSV格式
   - 自动触发文件下载
   - 文件名包含时间戳

### 后端API
- **新增路由**: `/admin/export_redemption_codes`
- **请求方式**: POST
- **请求参数**: `{"codes": ["CODE1", "CODE2", ...]}`
- **返回数据**: 包含兑换码详细信息的JSON数组

### 数据格式
**CSV导出格式**:
```csv
兑换码,积分,类型,状态,创建时间,过期时间,描述,使用次数
"ABC123DEF456",10,"一次性","有效","2025-07-20","2025-08-19","测试兑换码",0
```

## 使用场景

### 1. 批量分发兑换码
- 管理员创建大量兑换码后
- 选择需要分发的兑换码
- 批量复制后粘贴到通知群或邮件中

### 2. 兑换码数据备份
- 定期导出所有有效兑换码
- 生成包含详细信息的CSV文件
- 用于数据备份或外部分析

### 3. 活动管理
- 为特定活动创建的兑换码
- 批量导出用于活动统计
- 跟踪兑换码使用情况

## 安全考虑

1. **权限控制**: 只有管理员可以使用批量操作功能
2. **数据验证**: 后端验证兑换码存在性和权限
3. **操作日志**: 可以扩展添加批量操作的审计日志

## 兼容性

- **浏览器支持**: Chrome 66+, Firefox 63+, Safari 13.1+, Edge 79+
- **备用方案**: 对于不支持现代剪贴板API的浏览器提供备用复制方法
- **文件格式**: CSV格式兼容Excel、Google Sheets等常用软件

## 未来扩展

1. **批量编辑**: 支持批量修改兑换码的过期时间、描述等
2. **筛选导出**: 支持按条件筛选后批量导出
3. **模板导出**: 支持自定义导出格式和字段
4. **批量导入**: 支持从CSV文件批量导入兑换码

## 测试验证

已通过以下测试：
- ✅ 批量复制功能测试
- ✅ 批量导出功能测试  
- ✅ CSV文件格式验证
- ✅ 多浏览器兼容性测试
- ✅ 权限控制测试

## 总结

新增的批量操作功能大大提升了兑换码管理的效率，特别适合需要处理大量兑换码的场景。功能设计考虑了用户体验和数据安全，提供了完整的批量管理解决方案。
