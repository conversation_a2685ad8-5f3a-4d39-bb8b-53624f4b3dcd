#!/usr/bin/env python3
"""
简单测试导出功能
"""

import json
from datetime import datetime, timedelta
from redemption_system import RedemptionSystem

def test_redemption_system():
    """测试兑换码系统"""
    print("测试兑换码系统...")
    
    # 创建兑换码系统实例
    redemption_system = RedemptionSystem()
    
    # 创建一些测试兑换码
    print("创建测试兑换码...")
    codes = redemption_system.create_redemption_codes(
        code_type='one_time',
        points=10,
        count=3,
        expire_days=30,
        description='测试批量操作'
    )
    
    print(f"创建了 {len(codes)} 个兑换码:")
    for code in codes:
        print(f"  - {code}")
    
    # 获取所有兑换码
    all_codes = redemption_system.get_all_codes()
    print(f"\n系统中共有 {len(all_codes)} 个兑换码")
    
    # 测试导出数据格式
    print("\n测试导出数据格式:")
    export_data = []
    for code_info in all_codes[:3]:  # 只取前3个
        # 检查是否过期
        expire_time = datetime.fromisoformat(code_info['expire_at'])
        is_expired = datetime.now() > expire_time
        
        export_item = {
            'code': code_info['code'],
            'points': code_info['points'],
            'type': code_info['type'],
            'is_active': code_info['is_active'],
            'is_expired': is_expired,
            'created_at': code_info['created_at'],
            'expire_at': code_info['expire_at'],
            'description': code_info.get('description', ''),
            'used_count': code_info['used_count']
        }
        export_data.append(export_item)
    
    print("导出数据示例:")
    for item in export_data:
        status = '已过期' if item['is_expired'] else ('有效' if item['is_active'] else '已禁用')
        type_text = '一次性' if item['type'] == 'one_time' else '活动码'
        create_date = datetime.fromisoformat(item['created_at']).strftime('%Y-%m-%d')
        expire_date = datetime.fromisoformat(item['expire_at']).strftime('%Y-%m-%d')
        
        print(f"  兑换码: {item['code']}")
        print(f"    积分: {item['points']}")
        print(f"    类型: {type_text}")
        print(f"    状态: {status}")
        print(f"    创建: {create_date}")
        print(f"    过期: {expire_date}")
        print(f"    描述: {item['description'] or '无描述'}")
        print(f"    使用次数: {item['used_count']}")
        print()
    
    # 生成CSV格式
    print("生成CSV格式:")
    csv_content = '兑换码,积分,类型,状态,创建时间,过期时间,描述,使用次数\n'
    
    for item in export_data:
        status = '已过期' if item['is_expired'] else ('有效' if item['is_active'] else '已禁用')
        type_text = '一次性' if item['type'] == 'one_time' else '活动码'
        create_date = datetime.fromisoformat(item['created_at']).strftime('%Y-%m-%d')
        expire_date = datetime.fromisoformat(item['expire_at']).strftime('%Y-%m-%d')
        description = (item['description'] or '无描述').replace('"', '""')  # 转义双引号
        
        csv_content += f'"{item["code"]}",{item["points"]},"{type_text}","{status}","{create_date}","{expire_date}","{description}",{item["used_count"]}\n'
    
    print(csv_content)
    
    # 保存到文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    filename = f'test_export_{timestamp}.csv'
    
    with open(filename, 'w', encoding='utf-8-sig') as f:
        f.write(csv_content)
    
    print(f"CSV文件已保存为: {filename}")
    
    return True

if __name__ == "__main__":
    test_redemption_system()
